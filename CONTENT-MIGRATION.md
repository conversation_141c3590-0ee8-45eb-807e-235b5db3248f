# Portfolio Content Migration

Konten dari website portfolio https://rfiklz.my.id/ telah berhasil diekstrak dan diorganisir ke dalam file data terstruktur.

## 📁 File yang Dibuat

### 1. `portfolio-content.json`
File JSON yang berisi semua konten portfolio dalam format terstruktur.

### 2. `content-data.js`
File JavaScript/ES6 module yang berisi data portfolio dengan export statements untuk kemudahan import di React/Node.js.

## 📋 Struktur Konten

### Personal Information
- **Nama**: Rofik<PERSON>
- **Tagline**: "Vibecoder ✨"
- **Greeting**: "Wassup Homie! 👋🏻"
- **Bio**: Deskripsi personal tentang passion coding
- **Avatar**: Placeholder image

### Areas of Interest
- Web Technologies
- Ethical Hacking
- Blockchain
- Node.js
- React.js
- Next.js

### Technical Skills (8 skills)
Setiap skill memiliki:
- Nama teknologi
- URL icon dari vectorlogo.zone

### Featured Projects (3 projects)
1. **Cypher-Chain** - Blockchain visualization tool
2. **Phish-Guard** - Phishing detection browser extension  
3. **Vuln-Scanner** - Network vulnerability scanner

Setiap project memiliki:
- ID unik
- Title
- Description
- Image placeholder
- GitHub repository link
- Technologies used
- Category

### Contact Information
- GitHub: https://github.com/rfypych
- LinkedIn: https://linkedin.com/in/rfypych
- Website: https://rfiklz.my.id
- Terminal commands untuk contact section

### Navigation & Sections
- Menu navigasi dengan label bahasa Indonesia dan Inggris
- Section titles untuk setiap halaman

### Footer & Theme
- Footer text dan copyright
- Color scheme (emerald green theme)

## 🚀 Cara Menggunakan

### Untuk React Project:
```javascript
import { portfolioData, personal, projects, skills } from './content-data.js';

// Menggunakan semua data
console.log(portfolioData);

// Menggunakan data spesifik
console.log(personal.name); // "Rofikul Huda"
console.log(projects.length); // 3
```

### Untuk JSON:
```javascript
import portfolioContent from './portfolio-content.json';

// Akses data
const name = portfolioContent.personal.name;
const projectList = portfolioContent.projects;
```

## 📝 Catatan

1. **Images**: Saat ini menggunakan placeholder images. Ganti dengan gambar asli project jika tersedia.

2. **Contact**: Email tidak tersedia di website asli. Tambahkan jika diperlukan.

3. **Multilingual**: Data sudah disiapkan untuk bahasa Indonesia dan Inggris.

4. **Extensible**: Struktur data mudah diperluas untuk menambah konten baru.

## 🔄 Update Content

Untuk mengupdate konten:
1. Edit file `content-data.js` atau `portfolio-content.json`
2. Pastikan struktur data tetap konsisten
3. Update version control jika menggunakan Git

## 🎨 Theme Colors

- **Primary**: #10B981 (Emerald)
- **Background**: #0A0A10 (Dark)
- **Text**: #E5E7EB (Light Gray)
- **Accent**: #34D399 (Light Emerald)

Konten ini siap digunakan untuk project React, Vue, Angular, atau framework lainnya!
