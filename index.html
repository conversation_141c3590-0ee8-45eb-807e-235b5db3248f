<!DOCTYPE html>
<html lang="id" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portofolio | Rofikul Huda</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Menggunakan font-family yang lebih spesifik untuk menargetkan San Francisco di semua perangkat */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
            background-color: #0A0A10;
            color: #E5E7EB;
            overflow-x: hidden;
        }

        /* Latar Matrix */
        #matrix-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            opacity: 0.5;
        }

        /* Scanline Overlay */
        .scanline-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10;
            pointer-events: none;
            background: linear-gradient(rgba(0,0,0,0) 50%, rgba(0,0,0,0.2) 50%), linear-gradient(90deg, rgba(255,255,255,0.05) 1px, transparent 1px);
            background-size: 100% 4px, 3px 3px;
            animation: scanline 15s linear infinite;
        }
        @keyframes scanline {
            from { background-position: 0 0; }
            to { background-position: 0 100%; }
        }

        /* Preloader */
        #preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            background-color: #0A0A10;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }
        .loader {
            border: 4px solid rgba(255, 255, 255, 0.2);
            border-left-color: #10B981;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin { to { transform: rotate(360deg); } }

        /* Efek kaca cair (liquid glass) dengan efek 3D Tilt */
        .liquid-glass-effect {
            background: rgba(16, 185, 129, 0.05);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(52, 211, 153, 0.2);
            border-radius: 2rem;
            transition: all 0.3s ease-out;
            transform-style: preserve-3d;
        }
        .liquid-glass-effect:hover {
            border-color: rgba(52, 211, 153, 0.5);
            box-shadow: 0 10px 40px rgba(16, 185, 129, 0.1);
        }
        
        /* Kelas untuk navigasi aktif dan hover glitch */
        .nav-active {
            background-color: rgba(16, 185, 129, 0.3) !important;
            color: white !important;
        }
        .glitch-hover:hover {
            animation: glitch 0.3s linear;
        }
        @keyframes glitch{
          0% { text-shadow: .05em 0 0 rgba(255,0,0,.75), -.025em -.05em 0 rgba(0,255,0,.75), .025em .05em 0 rgba(0,0,255,.75); }
          14% { text-shadow: .05em 0 0 rgba(255,0,0,.75), -.025em -.05em 0 rgba(0,255,0,.75), .025em .05em 0 rgba(0,0,255,.75); }
          15% { text-shadow: -.05em -.025em 0 rgba(255,0,0,.75), .025em .025em 0 rgba(0,255,0,.75), -.05em -.05em 0 rgba(0,0,255,.75); }
          49% { text-shadow: -.05em -.025em 0 rgba(255,0,0,.75), .025em .025em 0 rgba(0,255,0,.75), -.05em -.05em 0 rgba(0,0,255,.75); }
          50% { text-shadow: .025em .05em 0 rgba(255,0,0,.75), .05em 0 0 rgba(0,255,0,.75), 0 -.05em 0 rgba(0,0,255,.75); }
          99% { text-shadow: .025em .05em 0 rgba(255,0,0,.75), .05em 0 0 rgba(0,255,0,.75), 0 -.05em 0 rgba(0,0,255,.75); }
          100% { text-shadow: -.025em 0 0 rgba(255,0,0,.75), -.025em -.025em 0 rgba(0,255,0,.75), -.025em -.05em 0 rgba(0,0,255,.75); }
        }
        
        #mobile-menu { max-height: 0; overflow: hidden; transition: max-height 0.5s ease-in-out, padding 0.5s ease-in-out; }
        #mobile-menu.open { padding: 0.5rem; }

        .page-content { animation: fadeIn 0.6s ease-in-out; }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px) scale(0.98); }
            to { opacity: 1; transform: translateY(0) scale(1); }
        }
        
        .project-card-image { border-radius: 1.75rem; }
        
        /* Terminal Styling */
        .terminal-window { font-family: 'Courier New', Courier, monospace; }
        .terminal-header { background-color: #1a1a1a; }
        .terminal-body { background-color: rgba(0,0,0,0.5); }
        .prompt::before { content: 'user@rfypych:~$ '; color: #10B981; }
        .blinking-cursor {
            display: inline-block;
            width: 0.5em;
            height: 1em;
            background-color: #10B981;
            animation: blink 1s step-end infinite;
        }
        @keyframes blink {
            from, to { background-color: transparent; }
            50% { background-color: #10B981; }
        }

        /* Animasi mengambang untuk hero card */
        #hero .tilt-card {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }
        
        /* Animasi pulse untuk emoji */
        .pulse-emoji {
            display: inline-block;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }

    </style>
</head>
<body class="text-gray-200 leading-relaxed">

    <!-- Preloader -->
    <div id="preloader"><div class="loader"></div></div>
    
    <!-- Latar Matrix -->
    <canvas id="matrix-canvas"></canvas>

    <!-- Overlay -->
    <div class="scanline-overlay"></div>
    
    <!-- Konten Utama -->
    <div class="relative z-10">
        <!-- Header -->
        <header class="p-4 fixed w-full top-0 z-50">
            <div class="container mx-auto px-4">
                 <nav id="main-nav" class="liquid-glass-effect p-2 px-6 flex justify-between items-center" style="border-radius: 9999px;">
                    <a href="#" data-page="hero" class="nav-link glitch-hover text-xl font-bold text-white transition-colors">Rofikul Huda</a>
                    <div class="hidden md:flex items-center space-x-2">
                        <a href="#" data-page="about" class="nav-link glitch-hover px-4 py-2 rounded-full transition-colors duration-300">Tentang</a>
                        <a href="#" data-page="skills" class="nav-link glitch-hover px-4 py-2 rounded-full transition-colors duration-300">Keahlian</a>
                        <a href="#" data-page="projects" class="nav-link glitch-hover px-4 py-2 rounded-full transition-colors duration-300">Proyek</a>
                        <a href="#" data-page="contact" class="nav-link glitch-hover px-4 py-2 rounded-full transition-colors duration-300">Kontak</a>
                    </div>
                    <div class="md:hidden"><button id="mobile-menu-button" class="text-white focus:outline-none"><svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path></svg></button></div>
                 </nav>
                 <div id="mobile-menu" class="md:hidden mt-2 liquid-glass-effect rounded-2xl">
                    <a href="#" data-page="about" class="mobile-nav-link block text-center py-3 text-white rounded-lg hover:bg-white/10">Tentang</a>
                    <a href="#" data-page="skills" class="mobile-nav-link block text-center py-3 text-white rounded-lg hover:bg-white/10">Keahlian</a>
                    <a href="#" data-page="projects" class="mobile-nav-link block text-center py-3 text-white rounded-lg hover:bg-white/10">Proyek</a>
                    <a href="#" data-page="contact" class="mobile-nav-link block text-center py-3 text-white rounded-lg hover:bg-white/10">Kontak</a>
                </div>
            </div>
        </header>

        <main id="main-content" class="container mx-auto px-4 pt-40" style="perspective: 1000px;">
            <!-- Section Hero -->
            <section id="hero" class="page-content min-h-[calc(100vh-10rem)] flex items-center text-center">
                <div class="w-full">
                    <div class="max-w-3xl mx-auto liquid-glass-effect p-8 md:p-12 shadow-2xl tilt-card">
                        <p class="text-2xl text-gray-300 mb-4">Wassup Homie! 👋🏻</p>
                        <h1 id="hero-title" class="text-4xl md:text-6xl font-bold text-white mb-4 mx-auto font-mono"></h1>
                        <p class="text-xl md:text-2xl text-emerald-300 mb-8">Vibecoder <span class="pulse-emoji">✨</span></p>
                        <a href="#" id="intro-button" class="bg-emerald-600 hover:bg-emerald-700 text-white font-bold py-3 px-8 rounded-full transition-transform transform hover:scale-105 duration-300 inline-block">
                            Jelajahi Sekarang
                        </a>
                    </div>
                </div>
            </section>

            <!-- Section Tentang Saya -->
            <section id="about" class="page-content hidden py-24">
                 <div class="max-w-5xl mx-auto liquid-glass-effect p-8 md:p-12 shadow-2xl tilt-card">
                    <h2 class="text-4xl font-bold text-white text-center mb-12">LET ME INTRODUCE MYSELF</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
                        <div class="md:col-span-1 flex justify-center"><img src="https://placehold.co/300x300/10B981/042f2e?text=RH" alt="Avatar Rofikul Huda" class="rounded-full w-48 h-48 md:w-64 md:h-64 object-cover border-4 border-white/20"></div>
                        <div class="md:col-span-2 text-gray-300 text-lg space-y-4">
                             <p>I totally got hooked on coding! Been diving deep and picking up bits and pieces, ya know? Feels like I'm learning something new every day... I think! 🤷‍♂️</p>
                             <p>My field of Interest's are gettin' the hang of <b class="text-emerald-300">Web Technologies, Ethical Hacking</b> and also in areas related to <b class="text-emerald-300">Blockchain.</b></p>
                            <p>Whenever possible, I also apply my passion for learnin' with <b class="text-emerald-300">Node.js</b> and Modern Javascript Library and Frameworks like <b class="text-emerald-300">React.js</b> and <b class="text-emerald-300">Next.js</b></p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Keahlian -->
            <section id="skills" class="page-content hidden py-24">
                <h2 class="text-4xl font-bold text-center text-white mb-16">Professional Skillset</h2>
                <div class="max-w-5xl mx-auto grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6">
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://www.vectorlogo.zone/logos/w3_html5/w3_html5-icon.svg" alt="HTML5 Icon" class="w-12 h-12"><span class="font-medium">HTML5</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://www.vectorlogo.zone/logos/w3_css/w3_css-icon.svg" alt="CSS3 Icon" class="w-12 h-12"><span class="font-medium">CSS3</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://www.vectorlogo.zone/logos/javascript/javascript-icon.svg" alt="JavaScript Icon" class="w-12 h-12"><span class="font-medium">JavaScript</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://www.vectorlogo.zone/logos/reactjs/reactjs-icon.svg" alt="React Icon" class="w-12 h-12"><span class="font-medium">React.js</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://www.vectorlogo.zone/logos/nextjs/nextjs-icon.svg" alt="Next.js Icon" class="w-12 h-12 bg-white rounded-full"><span class="font-medium">Next.js</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://www.vectorlogo.zone/logos/nodejs/nodejs-icon.svg" alt="Node.js Icon" class="w-12 h-12"><span class="font-medium">Node.js</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://www.vectorlogo.zone/logos/python/python-icon.svg" alt="Python Icon" class="w-12 h-12"><span class="font-medium">Python</span></div>
                    <div class="liquid-glass-effect p-4 flex flex-col items-center gap-3 tilt-card"><img src="https://www.vectorlogo.zone/logos/mysql/mysql-icon.svg" alt="MySQL Icon" class="w-12 h-12"><span class="font-medium">MySQL</span></div>
                </div>
            </section>

            <!-- Section Proyek -->
            <section id="projects" class="page-content hidden py-24">
                <h2 class="text-4xl font-bold text-center text-white mb-12">My Recent Works</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12">
                     <div class="liquid-glass-effect overflow-hidden shadow-lg flex flex-col p-2 tilt-card">
                        <img src="https://placehold.co/600x400/059669/d1fae5?text=Cypher-Chain" alt="Pratinjau Proyek Cypher-Chain" class="w-full h-48 object-cover project-card-image">
                        <div class="p-6 flex flex-col flex-grow">
                            <h3 class="text-2xl font-bold text-white mb-3">Cypher-Chain</h3>
                            <p class="text-gray-300 mb-4 flex-grow">A real-time blockchain visualization tool built with React and WebSockets, allowing users to track transactions and analyze block data on the Ethereum testnet.</p>
                            <a href="https://github.com/rfypych/cypher-chain" target="_blank" class="font-semibold text-emerald-400 hover:text-emerald-300 mt-auto">View on GitHub &rarr;</a>
                        </div>
                    </div>
                    <div class="liquid-glass-effect overflow-hidden shadow-lg flex flex-col p-2 tilt-card">
                        <img src="https://placehold.co/600x400/059669/d1fae5?text=Phish-Guard" alt="Pratinjau Proyek Phish-Guard" class="w-full h-48 object-cover project-card-image">
                        <div class="p-6 flex flex-col flex-grow">
                            <h3 class="text-2xl font-bold text-white mb-3">Phish-Guard</h3>
                            <p class="text-gray-300 mb-4 flex-grow">A browser extension using a pre-trained machine learning model to detect and warn users about potential phishing sites based on URL structure and content analysis.</p>
                            <a href="https://github.com/rfypych/phish-guard" target="_blank" class="font-semibold text-emerald-400 hover:text-emerald-300 mt-auto">View on GitHub &rarr;</a>
                        </div>
                    </div>
                    <div class="liquid-glass-effect overflow-hidden shadow-lg flex flex-col p-2 tilt-card">
                        <img src="https://placehold.co/600x400/059669/d1fae5?text=Vuln-Scanner" alt="Pratinjau Proyek Vuln-Scanner" class="w-full h-48 object-cover project-card-image">
                         <div class="p-6 flex flex-col flex-grow">
                            <h3 class="text-2xl font-bold text-white mb-3">Vuln-Scanner</h3>
                            <p class="text-gray-300 mb-4 flex-grow">A Python-based CLI tool for scanning local networks to identify common vulnerabilities (like open ports and outdated software) on connected devices.</p>
                            <a href="https://github.com/rfypych/vuln-scanner" target="_blank" class="font-semibold text-emerald-400 hover:text-emerald-300 mt-auto">View on GitHub &rarr;</a>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Kontak -->
            <section id="contact" class="page-content hidden py-24 text-center">
                 <div class="max-w-2xl mx-auto liquid-glass-effect p-1 shadow-2xl tilt-card terminal-window">
                    <div class="terminal-header p-2 rounded-t-xl flex items-center gap-2">
                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <div id="terminal-body" class="terminal-body p-4 text-left h-64 rounded-b-xl overflow-y-auto">
                        <!-- Konten terminal akan diisi oleh JS -->
                    </div>
                 </div>
            </section>
        </main>
        
        <!-- Footer -->
        <footer class="text-center py-10 mt-12 border-t border-white/10 space-y-2">
            <p class="text-gray-300">Developed by Rofikul Huda with 🧠</p>
            <p class="text-gray-400 text-sm">Copyright © 2025 rfypych</p>
        </footer>

    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tone/14.7.77/Tone.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // --- Variabel Global ---
            const preloader = document.getElementById('preloader');
            const navLinks = document.querySelectorAll('.nav-link');
            const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
            const pages = document.querySelectorAll('.page-content');
            const introButton = document.getElementById('intro-button');
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const tiltCards = document.querySelectorAll('.tilt-card');
            
            // --- Sound Engine ---
            let audioContextStarted = false;
            const synth = new Tone.Synth({ oscillator: { type: 'sine' }, envelope: { attack: 0.005, decay: 0.1, sustain: 0.3, release: 1 } }).toDestination();
            const typeSound = new Tone.MembraneSynth({ pitchDecay: 0.008, aattack: 0.001, decay: 0.1, sustain: 0.01, release: 0.1, octaves: 2}).toDestination();
            
            function startAudioContext() {
                if (audioContextStarted) return;
                Tone.start().then(() => {
                    audioContextStarted = true;
                    console.log("Audio Context initialized.");
                });
            }

            function throttle(func, limit) {
                let inThrottle;
                return function() {
                    if (!inThrottle) {
                        func.apply(this, arguments);
                        inThrottle = true;
                        setTimeout(() => inThrottle = false, limit);
                    }
                }
            }
            
            const throttledHoverSound = throttle(() => synth.triggerAttackRelease("C2", "8n"), 100);
            const throttledNavigateSound = throttle(() => synth.triggerAttackRelease("G2", "8n"), 100);
            const throttledTypeSound = throttle(() => typeSound.triggerAttack("C1", Tone.now(), 0.5), 80);

            // --- Efek Matrix ---
            const canvas = document.getElementById('matrix-canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            const katakana = 'アァカサタナハマヤャラワガザダバパイィキシチニヒミリヰギジヂビピウゥクスツヌフムユュルグズブヅプエェケセテネヘメレヱゲゼデベペオォコソトノホモヨョロヲゴゾドボポヴッン';
            const latin = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            const nums = '0123456789';
            const alphabet = katakana + latin + nums;
            const fontSize = 16;
            const columns = canvas.width / fontSize;
            const rainDrops = Array.from({ length: columns }).map(() => 1);

            function drawMatrix() {
                ctx.fillStyle = 'rgba(10, 10, 16, 0.05)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = '#10B981';
                ctx.font = fontSize + 'px monospace';
                for (let i = 0; i < rainDrops.length; i++) {
                    const text = alphabet.charAt(Math.floor(Math.random() * alphabet.length));
                    ctx.fillText(text, i * fontSize, rainDrops[i] * fontSize);
                    if (rainDrops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                        rainDrops[i] = 0;
                    }
                    rainDrops[i]++;
                }
            }
            setInterval(drawMatrix, 30);
            
            // --- Animasi Mengetik ---
            const heroTitle = document.getElementById('hero-title');
            function typeText(element, text, i, fnCallback) {
                if (i < text.length) {
                    element.innerHTML = text.substring(0, i + 1) + '<span class="blinking-cursor"></span>';
                    throttledTypeSound();
                    setTimeout(() => typeText(element, text, i + 1, fnCallback), 100);
                } else if (typeof fnCallback === 'function') {
                    element.querySelector('.blinking-cursor')?.remove();
                    setTimeout(fnCallback, 500);
                }
            }
            
            // --- Animasi Terminal Kontak ---
            const terminalBody = document.getElementById('terminal-body');
            const contactCommands = [
                { type: 'command', text: 'whoami' },
                { type: 'output', text: 'Rofikul Huda - Vibecoder' },
                { type: 'command', text: 'contact --list' },
                { type: 'output', text: 'Fetching contact list...' },
                { type: 'html', html: '<ul><li><a href="https://github.com/rfypych" target="_blank" class="text-emerald-300 hover:underline">GitHub: github.com/rfypych</a></li><li><a href="https://linkedin.com/in/rfypych" target="_blank" class="text-emerald-300 hover:underline">LinkedIn: linkedin.com/in/rfypych</a></li></ul>' },
                { type: 'prompt' }
            ];
            
            async function runTerminalSequence() {
                terminalBody.innerHTML = '';
                for (const item of contactCommands) {
                    await handleTerminalItem(item);
                }
            }

            function handleTerminalItem(item) {
                return new Promise(resolve => {
                    switch (item.type) {
                        case 'command':
                            const p = document.createElement('p');
                            p.innerHTML = `<span class="prompt"></span><span class="command-text"></span>`;
                            terminalBody.appendChild(p);
                            const commandSpan = p.querySelector('.command-text');
                            typeText(commandSpan, item.text, 0, resolve);
                            break;
                        case 'output':
                        case 'html':
                            const outputDiv = document.createElement('div');
                            outputDiv.className = item.type === 'output' ? 'py-1' : 'py-1';
                            outputDiv.innerHTML = item.text || item.html;
                            terminalBody.appendChild(outputDiv);
                            terminalBody.scrollTop = terminalBody.scrollHeight;
                            setTimeout(resolve, 300);
                            break;
                        case 'prompt':
                            const promptP = document.createElement('p');
                            promptP.innerHTML = `<span class="prompt"></span><span class="blinking-cursor"></span>`;
                            terminalBody.appendChild(promptP);
                            terminalBody.scrollTop = terminalBody.scrollHeight;
                            resolve();
                            break;
                    }
                });
            }

            // --- Navigasi Halaman ---
            function navigateTo(pageId, isInitialLoad = false) {
                if (!isInitialLoad) throttledNavigateSound();
                pages.forEach(page => page.classList.add('hidden'));
                const targetPage = document.getElementById(pageId);
                if (targetPage) targetPage.classList.remove('hidden');
                
                if (pageId === 'hero') {
                    typeText(heroTitle, "I'M ROFIKUL HUDA", 0, null);
                } else if (pageId === 'contact') {
                    runTerminalSequence();
                }

                navLinks.forEach(link => link.classList.toggle('nav-active', link.dataset.page === pageId));
                mobileMenu.classList.remove('open');
                mobileMenu.style.maxHeight = '0px';
            }
            
            // --- Event Listeners ---
            window.addEventListener('load', () => {
                preloader.style.opacity = '0';
                preloader.style.visibility = 'hidden';
            });
            
            // Event listener untuk desktop nav & tombol intro (dengan suara)
            [...navLinks, introButton].forEach(link => {
                link.addEventListener('mouseover', () => {
                    startAudioContext();
                    throttledHoverSound();
                });
                link.addEventListener('click', function(event) {
                    startAudioContext();
                    event.preventDefault();
                    navigateTo(this.dataset.page || 'about');
                });
            });
            
            // Event listener untuk mobile nav (tanpa suara hover)
            mobileNavLinks.forEach(link => {
                 link.addEventListener('click', function(event) {
                    startAudioContext();
                    event.preventDefault();
                    navigateTo(this.dataset.page || 'about');
                });
            });

            mobileMenuButton.addEventListener('click', function() {
                startAudioContext();
                mobileMenu.classList.toggle('open');
                mobileMenu.style.maxHeight = mobileMenu.classList.contains('open') ? mobileMenu.scrollHeight + 'px' : '0px';
            });
            
            // Efek 3D Tilt
            tiltCards.forEach(card => {
                card.addEventListener('mousemove', (e) => {
                    const rect = card.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    const { width, height } = rect;
                    const rotateX = (y - height / 2) / (height / 2) * -4;
                    const rotateY = (x - width / 2) / (width / 2) * 4;
                    card.style.transform = `rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(1.02)`;
                });
                card.addEventListener('mouseleave', () => card.style.transform = 'rotateX(0) rotateY(0) scale(1)');
            });

            // --- Inisialisasi ---
            navigateTo('hero', true);
        });
    </script>
</body>
</html>
