{"personal": {"name": "Rofikul Huda", "tagline": "Vibecoder ✨", "greeting": "Wassup Homie! 👋🏻", "title": "I'M ROFIKUL HUDA", "bio": "I totally got hooked on coding! Been diving deep and picking up bits and pieces, ya know? Feels like I'm learning something new every day... I think! 🤷‍♂️", "description": ["My field of Interest's are gettin' the hang of Web Technologies, Ethical Hacking and also in areas related to Blockchain.", "Whenever possible, I also apply my passion for learnin' with Node.js and Modern Javascript Library and Frameworks like React.js and Next.js"], "avatar": "https://placehold.co/300x300/10B981/042f2e?text=RH"}, "interests": ["Web Technologies", "Ethical Hacking", "Blockchain", "Node.js", "React.js", "Next.js"], "skills": [{"name": "HTML5", "icon": "https://www.vectorlogo.zone/logos/w3_html5/w3_html5-icon.svg"}, {"name": "CSS3", "icon": "https://www.vectorlogo.zone/logos/w3_css/w3_css-icon.svg"}, {"name": "JavaScript", "icon": "https://www.vectorlogo.zone/logos/javascript/javascript-icon.svg"}, {"name": "React.js", "icon": "https://www.vectorlogo.zone/logos/reactjs/reactjs-icon.svg"}, {"name": "Next.js", "icon": "https://www.vectorlogo.zone/logos/nextjs/nextjs-icon.svg"}, {"name": "Node.js", "icon": "https://www.vectorlogo.zone/logos/nodejs/nodejs-icon.svg"}, {"name": "Python", "icon": "https://www.vectorlogo.zone/logos/python/python-icon.svg"}, {"name": "MySQL", "icon": "https://www.vectorlogo.zone/logos/mysql/mysql-icon.svg"}], "projects": [{"id": "cypher-chain", "title": "Cypher-<PERSON>", "description": "A real-time blockchain visualization tool built with React and WebSockets, allowing users to track transactions and analyze block data on the Ethereum testnet.", "image": "https://placehold.co/600x400/059669/d1fae5?text=Cypher-Chain", "github": "https://github.com/rfypych/cypher-chain", "technologies": ["React", "WebSockets", "Ethereum", "Blockchain"]}, {"id": "phish-guard", "title": "Phish-Guard", "description": "A browser extension using a pre-trained machine learning model to detect and warn users about potential phishing sites based on URL structure and content analysis.", "image": "https://placehold.co/600x400/059669/d1fae5?text=Phish-Guard", "github": "https://github.com/rfypych/phish-guard", "technologies": ["Machine Learning", "Browser Extension", "Security", "JavaScript"]}, {"id": "vuln-scanner", "title": "<PERSON><PERSON>n-Sc<PERSON>r", "description": "A Python-based CLI tool for scanning local networks to identify common vulnerabilities (like open ports and outdated software) on connected devices.", "image": "https://placehold.co/600x400/059669/d1fae5?text=Vuln-Scanner", "github": "https://github.com/rfypych/vuln-scanner", "technologies": ["Python", "Network Security", "CLI", "Vulnerability Assessment"]}], "contact": {"github": "https://github.com/rfypych", "linkedin": "https://linkedin.com/in/rfypych", "terminal_commands": [{"type": "command", "text": "whoami"}, {"type": "output", "text": "Rofikul Huda - Vibecoder"}, {"type": "command", "text": "contact --list"}, {"type": "output", "text": "Fetching contact list..."}, {"type": "html", "html": "<ul><li><a href=\"https://github.com/rfypych\" target=\"_blank\" class=\"text-emerald-300 hover:underline\">GitHub: github.com/rfypych</a></li><li><a href=\"https://linkedin.com/in/rfypych\" target=\"_blank\" class=\"text-emerald-300 hover:underline\">LinkedIn: linkedin.com/in/rfypych</a></li></ul>"}, {"type": "prompt"}]}, "navigation": [{"id": "hero", "label": "Home", "label_id": "Tentang"}, {"id": "about", "label": "About", "label_id": "Tentang"}, {"id": "skills", "label": "Skills", "label_id": "<PERSON><PERSON><PERSON>"}, {"id": "projects", "label": "Projects", "label_id": "Proyek"}, {"id": "contact", "label": "Contact", "label_id": "Kontak"}], "sections": {"hero": {"title": "Hero Section", "cta_button": "<PERSON><PERSON><PERSON><PERSON>"}, "about": {"title": "LET ME INTRODUCE MYSELF"}, "skills": {"title": "Professional Skillset"}, "projects": {"title": "My Recent Works"}, "contact": {"title": "Contact"}}, "footer": {"text": "Developed by <PERSON><PERSON><PERSON><PERSON> with 🧠", "copyright": "Copyright © 2025 rfypych"}, "theme": {"primary_color": "#10B981", "background_color": "#0A0A10", "text_color": "#E5E7EB"}}