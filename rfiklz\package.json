{"name": "portfolio", "version": "0.1.0", "private": true, "dependencies": {"@react-pdf/renderer": "^2.2.0", "@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^12.1.4", "@testing-library/user-event": "^13.5.0", "axios": "^0.26.1", "bootstrap": "^5.1.3", "react": "^17.0.2", "react-bootstrap": "^2.2.1", "react-dom": "^17.0.2", "react-github-calendar": "^3.2.2", "react-helmet": "^6.1.0", "react-icons": "^4.8.0", "react-parallax-tilt": "^1.7.42", "react-pdf": "^5.7.1", "react-router-dom": "^6.2.2", "react-scripts": "5.0.0", "react-tsparticles": "^1.42.2", "typewriter-effect": "^2.18.2", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}